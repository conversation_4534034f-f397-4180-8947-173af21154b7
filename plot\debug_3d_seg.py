# debug_3d_seg.py
from napari import Viewer, run
from dataprocess.volume import Volume  # 根据实际文件位置调整导入路径
import numpy as np
from qtpy.QtCore import QThread, Signal, Qt
from qtpy.QtCore import QTimer
import time

# 创建 Viewer 实例
viewer = Viewer()

# 添加你的插件部件
dock_widget, seg3d_widget = viewer.window.add_plugin_dock_widget(
    plugin_name="3d-seg",  # 插件名称（与 contributions.yaml 中的 name 字段一致）
    widget_name="segmentation 3d",  # 部件的显示名称（display_name）
)

# ================== 加载真实数据 ==================
volume_path = "/data2/wyx/3d_seg/finetune_ready/em/val/em_s0_222.zst"  # 替换为真实路径
    # 在第50层添加矩形掩码
mask_path = (
    "/data2/wyx/3d_seg/finetune_ready/seg/val/mito_seg_222.zst"  # 替换为真实路径
)

volume_path_add = "/data2/wyx/3d_seg/finetune_ready/em/val/em_s0_223.zst"  # 替换为真实路径
    # 在第50层添加矩形掩码
mask_path_add = (
    "/data2/wyx/3d_seg/finetune_ready/seg/val/mito_seg_223.zst"  # 替换为真实路径
)

def add_sample_masks(direction, mask_path, interval=0, start_frame=0, end_frame=None):
    """
    添加示例掩码
    参数:
    - direction: 切片方向 ('x', 'y', 'z')
    - mask_path: 掩码文件路径
    - interval: 插入间隔 (0 表示只在首帧插入)
    - start_frame: 起始帧索引
    - end_frame: 结束帧索引 (None 表示到最后一帧)
    """
    # 根据方向设置维度顺序
    if direction == "x":
        # x轴切片：视图平面为yz平面
        viewer.dims.order = (2, 1, 0)
        print("切片方向设置为 x 轴 (视图平面: yz)")
    elif direction == "y":
        # y轴切片：视图平面为xz平面
        viewer.dims.order = (1, 0, 2)
        print("切片方向设置为 y 轴 (视图平面: xz)")
    elif direction == "z":  # 'z'
        # z轴切片：视图平面为xy平面（默认）
        viewer.dims.order = (0, 1, 2)
        print("切片方向设置为 z 轴 (视图平面: xy)")
    
    # 加载掩码数据
    volume = Volume(mask_path)
    volume.load()
    volume.volume_to_binary_8bit()
    volume.volume_to_frames(
        img_start=(0, 0),
        img_size=(1024, 1024),
        frame_start=0,
        frame_end=1024,
        direction=direction,
        use_pil=True,
    )
    
    # 获取当前体积数据的形状 (z, y, x)
    mask_layer = seg3d_widget.masks_layers[direction]
    volume_shape = mask_layer.data.shape
    
    # 确定结束帧
    if end_frame is None:
        if direction == "z":
            end_frame = volume_shape[0] - 1
        elif direction == "y":
            end_frame = volume_shape[1] - 1
        elif direction == "x":
            end_frame = volume_shape[2] - 1
    
    # 创建新的掩码数据副本
    new_mask_data = mask_layer.data.copy()
    
    # 确定要插入的帧索引
    if interval <= 0:  # 只插入首帧
        frame_indices = [start_frame]
    else:  # 按间隔插入
        frame_indices = range(start_frame, end_frame + 1, interval)
    
    # 获取样本掩码（使用第一帧）
    sample_mask = volume.frames[0]
    
    # 插入掩码到指定帧
    for frame_index in frame_indices:
        # 根据方向确定如何插入掩码
        if direction == "z":
            # z方向：掩码放在指定切片
            viewer.dims.current_step = (frame_index, 0, 0)  # 设置到第一层
            if 0 <= frame_index < volume_shape[0]:
                new_mask_data[frame_index] = volume.frames[frame_index]
                
                print(f"在z方向第{frame_index}帧添加掩码")
        elif direction == "y":
            # y方向：掩码放在指定切片
            viewer.dims.current_step = (frame_index,0, 0)  # 设置到第一层
            if 0 <= frame_index < volume_shape[1]:
                new_mask_data[:, frame_index, :] = volume.frames[frame_index]
                #mask_layer.data = new_mask_data
                print(f"在y方向第{frame_index}帧添加掩码")
        elif direction == "x":
            # x方向：掩码放在指定切片
            #viewer.dims.current_step = (0,0,frame_index)  # 设置到第一层
            viewer.dims.current_step = (frame_index,0,0)  # 设置到第一层
            if 0 <= frame_index < volume_shape[2]:
                new_mask_data[:, :, frame_index] = np.array(volume.frames[frame_index]).T
                #mask_layer.data = new_mask_data
                print(f"在x方向第{frame_index}帧添加掩码")
        mask_layer.data = new_mask_data
    # 更新掩码图层


    # 检查添加后的掩码是否全为0
    if direction == "z" and np.all(mask_layer.data[0] == 0):
        print(f"第0层的掩码全为0")
    elif direction == "y" and np.all(mask_layer.data[:, 0, :] == 0):
        print(f"y=0切片的掩码全为0")
    elif direction == "x" and np.all(mask_layer.data[:, :, 0] == 0):
        print(f"x=0切片的掩码全为0")


viewer.dims.current_step = (0, 0, 0)  # 设置到第一层


# 添加示例点提示和框提示
def add_sample_prompts():
    """添加示例点提示和框提示"""
    # 获取点图层和框图层
    points_layer = viewer.layers["points_prompts"]
    boxes_layer = viewer.layers["box_prompts"]

    # 正点（前景点）
    points_layer.add([0,578, 70])

    # 在第50层添加框提示
    box = np.array(
        [
            [0,876, 744],  # 左上角
            [0,1000, 744],  # 左上角
            [0,1000, 887],  # 右下角
            [0,876, 887]  # 左上角
        ]
    )
    boxes_layer.add_rectangles(box)



seg3d_widget._load_volume(volume_path)
#add_sample_prompts()

#viewer.dims.current_step = (1, 0, 0)  # 设置到第一层
# 方式2: 每20帧插入一帧
interval = 20  # 设置间隔
add_sample_masks("z", mask_path, interval=interval)
add_sample_masks("x", mask_path, interval=interval)
add_sample_masks("y", mask_path, interval=interval)
# add_sample_masks('z')

# viewer.dims.current_step = (3, 0, 0)  # 设置到第一层

# 运行预测

seg3d_widget._generate_condition_frames()
#seg3d_widget._run_propagation()





# 设置延迟（1秒后执行）
# QTimer.singleShot(1000, delayed_prediction)

# 启动napari

# seg3d_widget._load_volume(volume_path_add)

# add_sample_masks("x",mask_path_add)
# add_sample_masks("y",mask_path_add)
# add_sample_masks('z',mask_path_add)
# seg3d_widget._generate_condition_frames()
# seg3d_widget._run_propagation()
run()
